# Enhanced Push Notification System

## Overview
Complete push notification system with animated notification bell, daily quiz notifications, and winner announcements.

## 🎯 Features

### 1. Daily Quiz Completion Notifications
- **Trigger**: Automatically sent when student completes daily quiz
- **Content**: Score, percentage, coins earned, streak count
- **Personalization**: Different messages based on performance
- **Animation**: Bell shake and badge pulse on new notifications

### 2. Daily Winner Notifications (8 AM IST)
- **Schedule**: Every day at 8:00 AM IST via cron job
- **Recipients**: Top 3 daily quiz performers
- **Content**: Rank-specific congratulatory messages with emojis
- **Types**: 🥇 Champion, 🥈 Runner-up, 🥉 Top 3

### 3. Enhanced Notification Bell
- **Visual Feedback**: Animated bell shake on new notifications
- **Badge Animation**: Scale and pulse effects for unread count
- **Smart Icons**: Filled bell when notifications present
- **Real-time Updates**: Auto-refresh every 30 seconds
- **Dark Mode Support**: Adaptive styling

## 📱 Components

### NotificationBell.tsx
Enhanced notification bell with animations:
- Bell shake animation on new notifications
- Badge scale and pulse animations
- Real-time unread count updates
- Improved visual styling
- Dark/light theme support

### HeaderWithNotification.tsx
New header component with integrated notification bell:
- Flexible layout with back button support
- Centered title with notification bell
- Custom right component support
- Responsive design
- Theme-aware styling



## 🔧 Implementation Details

### New Notification Types
```typescript
'STUDENT_DAILY_QUIZ_COMPLETION' | 'STUDENT_DAILY_QUIZ_WINNER'
```

### Notification Icons & Colors
- **Quiz Completion**: `checkmark-done-circle` (Green #4CAF50)
- **Quiz Winner**: `trophy` (Gold #FFD700)

### Animation System
```typescript
// Bell shake animation
const shakeBell = () => {
  Animated.sequence([
    // Shake left-right-left-center
  ]).start();
};

// Badge animations
const animateBadge = () => {
  // Scale animation + Pulse loop
};
```

### Firebase Integration
Enhanced notification handling:
- Action-based navigation routing
- Daily quiz result navigation
- Leaderboard navigation
- Wallet navigation

## 🚀 Usage Examples

### Trigger Daily Quiz Completion
```typescript
import { triggerDailyQuizCompletionNotification } from '../services/notificationService';

await triggerDailyQuizCompletionNotification(8, 10, 15, 5);
// score: 8, total: 10, coins: 15, streak: 5
```

### Trigger Daily Winner
```typescript
import { triggerDailyQuizWinnerNotification } from '../services/notificationService';

await triggerDailyQuizWinnerNotification(1, 85, 25);
// rank: 1, score: 85, coins: 25
```

### Use Enhanced Header
```typescript
import HeaderWithNotification from '../CommonComponents/HeaderWithNotification';

<HeaderWithNotification
  title="Daily Quiz"
  showBackButton={true}
  showNotification={true}
/>
```

## 📋 Notification Messages

### Quiz Completion (Performance-based)
- **80%+ Score**: "🏆 Excellent Performance! Outstanding! You scored 8/10 (80%) and earned 15 coins. Keep up the great work! 🔥 Current streak: 5 days!"
- **60-79% Score**: "👍 Good Job! Well done! You scored 6/10 (60%) and earned 12 coins. You're improving! 💪 Current streak: 3 days!"
- **<60% Score**: "📚 Keep Learning! You scored 4/10 (40%) and earned 8 coins. Practice makes perfect! 🎯 Current streak: 1 day!"

### Daily Winners
- **1st Place**: "🥇 Daily Quiz Champion! Congratulations! You're today's #1 quiz champion with 85 points and earned 25 coins! Amazing performance! 🎉"
- **2nd Place**: "🥈 Daily Quiz Runner-up! Excellent work! You secured 2nd place with 82 points and earned 20 coins! Keep it up! 🔥"
- **3rd Place**: "🥉 Daily Quiz Top 3! Great job! You made it to 3rd place with 78 points and earned 15 coins! Well done! 💪"

## 🎨 Visual Enhancements

### Notification Bell Improvements
- Larger, more prominent badge (22px vs 20px)
- Enhanced shadow and elevation
- Better border styling
- Improved typography (font weight 800)
- Smooth animations and transitions

### Header Component Features
- Flexible 3-section layout
- Rounded back button with background
- Improved shadows and elevation
- Better spacing and alignment
- Theme-aware colors



## 🔄 Integration Flow

### Quiz Completion Flow
1. Student completes daily quiz
2. `uwhiz-server` saves result
3. Calls main server notification API
4. Push notification sent to device
5. Bell animates and badge updates
6. User taps notification → navigates to results

### Daily Winner Flow
1. Cron job runs at 8 AM IST
2. Fetches today's leaderboard
3. Identifies top 3 performers
4. Sends rank-specific notifications
5. Users receive congratulatory messages
6. Tap notification → navigates to leaderboard

## 📦 Dependencies
- `@react-native-firebase/messaging`: Push notifications
- `react-native-vector-icons/Ionicons`: Icons
- `react-native-async-storage`: Token storage
- `@react-navigation/native`: Navigation

## 🎯 Next Steps
1. Add notification sound customization
2. Implement notification categories
3. Add rich media notifications
4. Create notification history
5. Add notification preferences
6. Implement notification scheduling
