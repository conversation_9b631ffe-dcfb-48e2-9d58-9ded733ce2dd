import dotenv from 'dotenv';
import prisma from '../config/prismaClient';
import { sendDailyQuizCompletionNotification, sendStreakReminderNotification } from '../services/pushNotificationService';

// Load environment variables
dotenv.config();

async function testNotificationSystem() {
  try {
    console.log('Testing Notification System...\n');

    const fcmTokenCount = await prisma.fcmToken.count();
    console.log(`Database - FCM Tokens: ${fcmTokenCount}`);

    const testStudentId = '1eb57d0a-ce89-44f5-9c0d-2a9b65eb52b0';

    console.log('\nTesting Daily Quiz Completion...');
    try {
      const result = await sendDailyQuizCompletionNotification({
        studentId: testStudentId,
        score: 8,
        totalQuestions: 10,
        coinEarnings: 15,
        streakCount: 3,
        timeTaken: 300,
        completedFrom: 'mobile'
      });

      console.log('Daily quiz completion test:', result.success ? 'PASSED' : 'FAILED');
      if (!result.success) console.log('Error:', result.error);
    } catch (error) {
      console.log('Daily quiz completion test FAILED:', error);
    }

    console.log('\nTesting Streak Reminder...');
    try {
      const result = await sendStreakReminderNotification({
        studentId: testStudentId,
        currentStreak: 5,
        hoursRemaining: 4
      });

      console.log('Streak reminder test:', result.success ? 'PASSED' : 'FAILED');
      if (!result.success) console.log('Error:', result.error);
    } catch (error) {
      console.log('Streak reminder test FAILED:', error);
    }

    console.log('\nScheduled Jobs Status:');
    console.log('- Daily Winner: 8:00 AM IST daily');
    console.log('- Streak Reminder: Every hour (sends at 8:00 PM IST)');

    console.log('\nNotification System Test Complete!');

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testNotificationSystem();
