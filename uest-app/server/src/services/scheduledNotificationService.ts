import cron from 'node-cron';
import { calculateAndNotifyDailyWinners } from './dailyWinnerService';
import { streakService } from './streakService';

export class ScheduledNotificationService {
  private static instance: ScheduledNotificationService;
  private dailyWinnerJob: cron.ScheduledTask | null = null;

  private constructor() {}

  public static getInstance(): ScheduledNotificationService {
    if (!ScheduledNotificationService.instance) {
      ScheduledNotificationService.instance = new ScheduledNotificationService();
    }
    return ScheduledNotificationService.instance;
  }

  public startDailyWinnerNotifications(): void {
    // Stop existing job if running
    this.stopDailyWinnerNotifications();

    // Schedule daily winner notifications at 8:00 AM IST (automatic)
    // Cron format: second minute hour day month dayOfWeek
    // '0 0 8 * * *' means every day at 8:00 AM
    this.dailyWinnerJob = cron.schedule('0 0 8 * * *', async () => {
      console.log('🏆 Running AUTOMATIC daily winner notification job at:', new Date().toISOString());

      try {
        const result = await calculateAndNotifyDailyWinners();

        if (result.success) {
          console.log(`✅ AUTOMATIC daily winner notifications sent successfully to ${result.winners?.length || 0} winners`);

          if (result.winners && result.winners.length > 0) {
            console.log('🎉 Today\'s automatic winners:');
            result.winners.forEach(winner => {
              console.log(`   ${winner.rank}. ${winner.firstName || 'Unknown'} ${winner.lastName || 'Student'} - ${winner.score} points (${winner.coinEarnings} coins)`);
            });
          } else {
            console.log('No participants found for yesterday\'s quiz - no winners to announce');
          }
        } else {
          console.error('Failed to send automatic daily winner notifications:', result.error);
        }
      } catch (error) {
        console.error('Error in automatic daily winner notification job:', error);
      }
    }, {
      scheduled: true,
      timezone: 'Asia/Kolkata' // IST timezone
    });

    console.log('🕐 AUTOMATIC daily winner notification job scheduled for 8:00 AM IST daily');
  }

  public stopDailyWinnerNotifications(): void {
    if (this.dailyWinnerJob) {
      this.dailyWinnerJob.stop();
      this.dailyWinnerJob = null;
      console.log('🛑 Daily winner notification job stopped');
    }
  }

  public async triggerDailyWinnerNotificationManually(): Promise<any> {
    console.log('🔧 Manually triggering daily winner notifications...');
    
    try {
      const result = await calculateAndNotifyDailyWinners();
      console.log('Manual trigger result:', result);
      return result;
    } catch (error) {
      console.error('Error in manual trigger:', error);
      throw error;
    }
  }

  public getDailyWinnerJobStatus(): { isRunning: boolean; scheduledTime: string } {
    if (!this.dailyWinnerJob) {
      return { isRunning: false, scheduledTime: 'Not scheduled' };
    }

    return {
      isRunning: true,
      scheduledTime: '8:00 AM IST daily'
    };
  }

  public startAllScheduledJobs(): void {
    console.log('🚀 Starting all scheduled notification jobs...');
    this.startDailyWinnerNotifications();

    // Start streak reminder system
    console.log('🔔 Starting streak reminder system...');
    streakService.startStreakReminderSystem();
  }

  public stopAllScheduledJobs(): void {
    console.log('🛑 Stopping all scheduled notification jobs...');
    this.stopDailyWinnerNotifications();

    // Stop streak reminder system
    console.log('🔔 Stopping streak reminder system...');
    streakService.stopStreakReminderSystem();
  }
}

// Export singleton instance
export const scheduledNotificationService = ScheduledNotificationService.getInstance();
