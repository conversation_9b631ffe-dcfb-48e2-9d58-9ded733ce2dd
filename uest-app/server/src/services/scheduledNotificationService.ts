import cron from 'node-cron';
import { calculateAndNotifyDailyWinners } from './dailyWinnerService';
import { streakService } from './streakService';

export class ScheduledNotificationService {
  private static instance: ScheduledNotificationService;
  private dailyWinnerJob: cron.ScheduledTask | null = null;

  private constructor() {}

  public static getInstance(): ScheduledNotificationService {
    if (!ScheduledNotificationService.instance) {
      ScheduledNotificationService.instance = new ScheduledNotificationService();
    }
    return ScheduledNotificationService.instance;
  }

  public startDailyWinnerNotifications(): void {
    this.stopDailyWinnerNotifications();

    this.dailyWinnerJob = cron.schedule('0 0 8 * * *', async () => {
      try {
        const result = await calculateAndNotifyDailyWinners();

        if (result.success && result.winners?.length) {
          console.log(`Daily winners notified: ${result.winners.length}`);
        } else if (!result.success) {
          console.error('Daily winner notification failed:', result.error);
        }
      } catch (error) {
        console.error('Daily winner job error:', error);
      }
    }, {
      scheduled: true,
      timezone: 'Asia/Kolkata'
    });

    console.log('Daily winner notifications scheduled for 8:00 AM IST');
  }

  public stopDailyWinnerNotifications(): void {
    if (this.dailyWinnerJob) {
      this.dailyWinnerJob.stop();
      this.dailyWinnerJob = null;
    }
  }

  public async triggerDailyWinnerNotificationManually(): Promise<any> {
    try {
      return await calculateAndNotifyDailyWinners();
    } catch (error) {
      console.error('Manual trigger error:', error);
      throw error;
    }
  }

  public getDailyWinnerJobStatus(): { isRunning: boolean; scheduledTime: string } {
    return this.dailyWinnerJob
      ? { isRunning: true, scheduledTime: '8:00 AM IST daily' }
      : { isRunning: false, scheduledTime: 'Not scheduled' };
  }

  public startAllScheduledJobs(): void {
    console.log('Starting scheduled notification jobs...');
    this.startDailyWinnerNotifications();
    streakService.startStreakReminderSystem();
  }

  public stopAllScheduledJobs(): void {
    console.log('Stopping scheduled notification jobs...');
    this.stopDailyWinnerNotifications();
    streakService.stopStreakReminderSystem();
  }
}

// Export singleton instance
export const scheduledNotificationService = ScheduledNotificationService.getInstance();
