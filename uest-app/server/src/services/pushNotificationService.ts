import { messaging } from '@/config/firebase';
import { UserType } from '@prisma/client';
import { getUserFcmTokens, getMultipleUsersFcmTokens, cleanupInvalidTokens } from './fcmTokenService';
import { getAdminUserIds, createNotification } from '@/utils/notifications';

export interface PushNotificationData {
  title: string;
  message: string;
  data?: Record<string, string>;
  imageUrl?: string;
  clickAction?: string;
}

export interface SingleUserNotification extends PushNotificationData {
  userId: string;
  userType: UserType;
}

export interface MultipleUsersNotification extends PushNotificationData {
  userIds: string[];
  userType: UserType;
}

const createNotificationPayload = (notification: SingleUserNotification | MultipleUsersNotification, isMultiple = false) => {
  const { title, message, data, imageUrl, clickAction } = notification;
  return {
    notification: {
      title,
      body: message,
      ...(imageUrl && { imageUrl })
    },
    data: {
      ...data,
      ...(clickAction && { click_action: clickAction }),
      ...(!isMultiple && { userId: (notification as SingleUserNotification).userId }),
      userType: notification.userType,
      timestamp: new Date().toISOString()
    }
  };
};

export const sendPushNotificationToUser = async (notification: SingleUserNotification) => {
  try {
    if (!messaging) {
      return { success: false, error: 'Firebase not configured' };
    }

    const { userId, userType } = notification;
    const userTokens = await getUserFcmTokens(userId, userType);

    if (userTokens.length === 0) {
      return { success: true, message: 'No tokens found' };
    }

    const tokens = userTokens.map(t => t.token);
    const payload = { ...createNotificationPayload(notification), tokens };
    const response = await messaging.sendEachForMulticast(payload);

    const failedTokens = response.responses
      .map((resp, idx) => !resp.success ? tokens[idx] : null)
      .filter(Boolean) as string[];

    if (failedTokens.length > 0) {
      await cleanupInvalidTokens(failedTokens);
    }

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      totalTokens: tokens.length
    };

  } catch (error) {
    console.error('Error sending push notification to user:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

export const sendPushNotificationToMultipleUsers = async (notification: MultipleUsersNotification) => {
  try {
    if (!messaging) {
      return { success: false, error: 'Firebase not configured' };
    }

    const { userIds, userType } = notification;
    const userTokens = await getMultipleUsersFcmTokens(userIds, userType);

    if (userTokens.length === 0) {
      return { success: true, message: 'No tokens found' };
    }

    const tokens = userTokens.map(t => t.token);
    const payload = { ...createNotificationPayload(notification, true), tokens };
    const response = await messaging.sendEachForMulticast(payload);

    const failedTokens = response.responses
      .map((resp, idx) => !resp.success ? tokens[idx] : null)
      .filter(Boolean) as string[];

    if (failedTokens.length > 0) {
      await cleanupInvalidTokens(failedTokens);
    }

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      totalTokens: tokens.length,
      totalUsers: userIds.length
    };

  } catch (error) {
    console.error('Error sending push notification to multiple users:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

export const sendPushNotificationToAllAdmins = async (notification: PushNotificationData) => {
  try {
    const adminIds = await getAdminUserIds();

    if (adminIds.length === 0) {
      console.log('No admin users found');
      return { success: true, message: 'No admin users found' };
    }

    return await sendPushNotificationToMultipleUsers({
      ...notification,
      userIds: adminIds,
      userType: UserType.ADMIN
    });

  } catch (error) {
    console.error('Error sending push notification to all admins:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

export interface DailyQuizCompletionData {
  studentId: string;
  score: number;
  totalQuestions: number;
  coinEarnings: number;
  streakCount?: number;
  timeTaken?: number;
  completedFrom?: 'mobile' | 'web';
}

const getQuizCompletionMessage = (score: number, totalQuestions: number, coinEarnings: number, streakCount: number) => {
  const percentage = Math.round((score / totalQuestions) * 100);
  const baseMessage = `You scored ${score}/${totalQuestions} (${percentage}%) and earned ${coinEarnings} coins.`;
  const streakMessage = streakCount > 0 ? ` Current streak: ${streakCount} days!` : '';

  const templates = [
    { threshold: 80, title: '🏆 Excellent Performance!', message: `Outstanding! ${baseMessage} Keep up the great work! 🔥${streakMessage}` },
    { threshold: 60, title: '👍 Good Job!', message: `Well done! ${baseMessage} You're improving! 💪${streakMessage}` },
    { threshold: 0, title: '📚 Keep Learning!', message: `${baseMessage} Practice makes perfect! 🎯${streakMessage}` }
  ];

  return templates.find(t => percentage >= t.threshold) || templates[2];
};

export const sendDailyQuizCompletionNotification = async (data: DailyQuizCompletionData) => {
  try {
    const { studentId, score, totalQuestions, coinEarnings, streakCount = 0, timeTaken, completedFrom = 'mobile' } = data;
    const { title, message } = getQuizCompletionMessage(score, totalQuestions, coinEarnings, streakCount);
    const sourceText = completedFrom === 'web' ? '💻' : '📱';

    const notificationData = {
      actionType: 'OPEN_DAILY_QUIZ_RESULTS',
      score: score.toString(),
      totalQuestions: totalQuestions.toString(),
      percentage: Math.round((score / totalQuestions) * 100).toString(),
      coinEarnings: coinEarnings.toString(),
      streakCount: streakCount.toString(),
      timeTaken: timeTaken?.toString() || '0',
      completedFrom,
      timestamp: new Date().toISOString()
    };

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: 'STUDENT_DAILY_QUIZ_COMPLETION' as any,
      title,
      message,
      data: notificationData
    });

    return await sendPushNotificationToUser({
      userId: studentId,
      userType: UserType.STUDENT,
      title: `${sourceText} ${title}`,
      message,
      data: notificationData,
      clickAction: 'OPEN_DAILY_QUIZ_RESULTS'
    });

  } catch (error) {
    console.error('Error sending daily quiz completion notification:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Daily Winner Notification
export interface DailyWinnerData {
  studentId: string;
  rank: number;
  score: number;
  firstName?: string;
  lastName?: string;
  coinEarnings: number;
}

const getWinnerMessage = (rank: number, score: number, coinEarnings: number) => {
  const templates = {
    1: { emoji: '🥇', title: 'Daily Quiz Champion!', message: `Congratulations! You're today's #1 quiz champion with ${score} points and earned ${coinEarnings} coins! Amazing performance! 🎉` },
    2: { emoji: '🥈', title: 'Daily Quiz Runner-up!', message: `Excellent work! You secured 2nd place with ${score} points and earned ${coinEarnings} coins! Keep it up! 🔥` },
    3: { emoji: '🥉', title: 'Daily Quiz Top 3!', message: `Great job! You made it to 3rd place with ${score} points and earned ${coinEarnings} coins! Well done! 💪` }
  };

  const template = templates[rank as keyof typeof templates];
  return template ? { title: `${template.emoji} ${template.title}`, message: template.message } : null;
};

export const sendDailyWinnerNotification = async (winners: DailyWinnerData[]) => {
  try {
    const notifications = winners.map(async (winner) => {
      const { studentId, rank, score, firstName, lastName, coinEarnings } = winner;
      const winnerMessage = getWinnerMessage(rank, score, coinEarnings);

      if (!winnerMessage) return null;

      const notificationData = {
        actionType: 'OPEN_LEADERBOARD',
        rank: rank.toString(),
        score: score.toString(),
        coinEarnings: coinEarnings.toString(),
        winnerType: 'DAILY_WINNER',
        firstName: firstName || '',
        lastName: lastName || '',
        timestamp: new Date().toISOString()
      };

      await createNotification({
        userId: studentId,
        userType: UserType.STUDENT,
        type: 'STUDENT_DAILY_QUIZ_WINNER' as any,
        title: winnerMessage.title,
        message: winnerMessage.message,
        data: notificationData
      });

      return sendPushNotificationToUser({
        userId: studentId,
        userType: UserType.STUDENT,
        title: winnerMessage.title,
        message: winnerMessage.message,
        data: notificationData,
        clickAction: 'OPEN_LEADERBOARD'
      });
    });

    const results = await Promise.allSettled(notifications.filter(n => n !== null));

    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const failureCount = results.filter(r => r.status === 'rejected').length;

    return {
      success: true,
      successCount,
      failureCount,
      totalWinners: winners.length
    };

  } catch (error) {
    console.error('Error sending daily winner notifications:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Streak Reminder Notification
export interface StreakReminderData {
  studentId: string;
  currentStreak: number;
  hoursRemaining: number;
}

const getStreakReminderMessage = (currentStreak: number, hoursRemaining: number) => {
  const messages = [
    `🔥 Start your daily quiz streak! Only ${hoursRemaining} hours left to take today's quiz. Don't miss out!`,
    `🔥 Keep your streak alive! You have ${hoursRemaining} hours left to maintain your ${currentStreak}-day streak. Take today's quiz now!`,
    `🔥 Don't break your ${currentStreak}-day streak! Only ${hoursRemaining} hours remaining to take today's quiz. Keep it going! 💪`
  ];

  return currentStreak === 0 ? messages[0] : currentStreak === 1 ? messages[1] : messages[2];
};

export const sendStreakReminderNotification = async (data: StreakReminderData) => {
  try {
    const { studentId, currentStreak, hoursRemaining } = data;
    const title = '⏰ Streak Reminder!';
    const message = getStreakReminderMessage(currentStreak, hoursRemaining);

    const notificationData = {
      actionType: 'OPEN_DAILY_QUIZ',
      currentStreak: currentStreak.toString(),
      hoursRemaining: hoursRemaining.toString(),
      reminderType: 'STREAK_REMINDER',
      timestamp: new Date().toISOString()
    };

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: 'STUDENT_DAILY_QUIZ_REMINDER' as any,
      title,
      message,
      data: notificationData
    });

    return await sendPushNotificationToUser({
      userId: studentId,
      userType: UserType.STUDENT,
      title,
      message,
      data: notificationData,
      clickAction: 'OPEN_DAILY_QUIZ'
    });

  } catch (error) {
    console.error('Error sending streak reminder notification:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
