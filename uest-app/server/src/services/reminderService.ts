import cron from 'node-cron';
import prisma from '@/config/prismaClient';
import { sendPushNotificationToUser } from './pushNotificationService';
import { createNotification } from '@/utils/notifications';
import { UserType, NotificationType, ReminderType, ReminderStatus } from '@prisma/client';

export class ReminderService {
  private static instance: ReminderService;
  private streakReminderJob: cron.ScheduledTask | null = null;
  private dailyQuizReminderJob: cron.ScheduledTask | null = null;

  private constructor() {}

  public static getInstance(): ReminderService {
    if (!ReminderService.instance) {
      ReminderService.instance = new ReminderService();
    }
    return ReminderService.instance;
  }

  // Start all reminder jobs
  public startAllReminderJobs(): void {
    console.log('🚀 Starting all reminder jobs...');
    this.startStreakReminderJob();
    this.startDailyQuizReminderJob();
  }

  // Stop all reminder jobs
  public stopAllReminderJobs(): void {
    console.log('🛑 Stopping all reminder jobs...');
    this.stopStreakReminderJob();
    this.stopDailyQuizReminderJob();
  }

  // Start streak reminder job (runs every hour to check for pending reminders)
  public startStreakReminderJob(): void {
    this.stopStreakReminderJob();

    // Run every hour to check for pending streak reminders
    this.streakReminderJob = cron.schedule('0 * * * *', async () => {
      console.log('⏰ Checking for pending streak reminders...');
      await this.processStreakReminders();
    }, {
      scheduled: true,
      timezone: 'Asia/Kolkata'
    });

    console.log('⏰ Streak reminder job started (runs every hour)');
  }

  // Stop streak reminder job
  public stopStreakReminderJob(): void {
    if (this.streakReminderJob) {
      this.streakReminderJob.stop();
      this.streakReminderJob = null;
      console.log('🛑 Streak reminder job stopped');
    }
  }

  // Start daily quiz reminder job (runs at 6 PM IST daily)
  public startDailyQuizReminderJob(): void {
    this.stopDailyQuizReminderJob();

    // Run at 6 PM IST daily for general daily quiz reminders
    this.dailyQuizReminderJob = cron.schedule('0 18 * * *', async () => {
      console.log('📚 Sending daily quiz reminders...');
      await this.sendDailyQuizReminders();
    }, {
      scheduled: true,
      timezone: 'Asia/Kolkata'
    });

    console.log('📚 Daily quiz reminder job started (runs at 6 PM IST)');
  }

  // Stop daily quiz reminder job
  public stopDailyQuizReminderJob(): void {
    if (this.dailyQuizReminderJob) {
      this.dailyQuizReminderJob.stop();
      this.dailyQuizReminderJob = null;
      console.log('Daily quiz reminder job stopped');
    }
  }

  // Process pending streak reminders
  private async processStreakReminders(): Promise<void> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Get pending streak reminders that should be sent now
      const pendingReminders = await prisma.notificationReminder.findMany({
        where: {
          reminderType: ReminderType.STREAK_REMINDER,
          status: ReminderStatus.PENDING,
          scheduledFor: {
            gte: oneHourAgo,
            lte: now
          }
        },
        orderBy: {
          scheduledFor: 'asc'
        }
      });

      console.log(`Found ${pendingReminders.length} pending streak reminders`);

      for (const reminder of pendingReminders) {
        try {
          // Check if student has already completed today's quiz
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const todayParticipation = await prisma.dailyQuizParticipation.findUnique({
            where: {
              studentId_quizDate: {
                studentId: reminder.userId,
                quizDate: today
              }
            }
          });

          if (todayParticipation) {
            // Student already completed today's quiz, cancel reminder
            await prisma.notificationReminder.update({
              where: { id: reminder.id },
              data: { status: ReminderStatus.CANCELLED }
            });
            continue;
          }

          // Get student's current streak
          const streakRecord = await prisma.dailyQuizStreak.findUnique({
            where: { studentId: reminder.userId }
          });

          if (!streakRecord || streakRecord.currentStreak === 0) {
            // No active streak, cancel reminder
            await prisma.notificationReminder.update({
              where: { id: reminder.id },
              data: { status: ReminderStatus.CANCELLED }
            });
            continue;
          }

          // Send streak reminder notification
          await this.sendStreakReminderNotification(reminder.userId, streakRecord.currentStreak);

          // Mark reminder as sent
          await prisma.notificationReminder.update({
            where: { id: reminder.id },
            data: { 
              status: ReminderStatus.SENT,
              sentAt: now
            }
          });

          console.log(`✅ Sent streak reminder to student ${reminder.userId}`);

        } catch (error) {
          console.error(`❌ Failed to send streak reminder to ${reminder.userId}:`, error);
          
          // Mark reminder as failed
          await prisma.notificationReminder.update({
            where: { id: reminder.id },
            data: { status: ReminderStatus.FAILED }
          });
        }
      }

    } catch (error) {
      console.error('Error processing streak reminders:', error);
    }
  }

  // Send daily quiz reminders to students who haven't completed today's quiz
  private async sendDailyQuizReminders(): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all students who have active streaks but haven't completed today's quiz
      const activeStreaks = await prisma.dailyQuizStreak.findMany({
        where: {
          isActive: true,
          currentStreak: {
            gt: 0
          }
        }
      });

      console.log(`Found ${activeStreaks.length} students with active streaks`);

      for (const streak of activeStreaks) {
        try {
          // Check if student completed today's quiz
          const todayParticipation = await prisma.dailyQuizParticipation.findUnique({
            where: {
              studentId_quizDate: {
                studentId: streak.studentId,
                quizDate: today
              }
            }
          });

          if (!todayParticipation) {
            // Student hasn't completed today's quiz, send reminder
            await this.sendDailyQuizReminderNotification(streak.studentId, streak.currentStreak);
            console.log(`Sent daily quiz reminder to student ${streak.studentId}`);
          }

        } catch (error) {
          console.error(`Failed to send daily quiz reminder to ${streak.studentId}:`, error);
        }
      }

    } catch (error) {
      console.error('Error sending daily quiz reminders:', error);
    }
  }

  // Send streak reminder notification
  private async sendStreakReminderNotification(studentId: string, currentStreak: number): Promise<void> {
    const title = `🔥 Don't Break Your ${currentStreak}-Day Streak!`;
    const message = `You have 4 hours left to complete today's daily quiz and maintain your ${currentStreak}-day streak. Keep the momentum going! 💪`;

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: NotificationType.STUDENT_STREAK_REMINDER,
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ',
        currentStreak: currentStreak.toString(),
        hoursLeft: '4',
        urgent: true,
        timestamp: new Date().toISOString()
      }
    });
  }

  // Send daily quiz reminder notification
  private async sendDailyQuizReminderNotification(studentId: string, currentStreak: number): Promise<void> {
    const title = '📚 Daily Quiz Available!';
    const message = currentStreak > 0 
      ? `Your daily quiz is ready! Continue your ${currentStreak}-day streak and earn more coins. 🎯`
      : `Your daily quiz is ready! Start building your streak and earn coins. 🌟`;

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: NotificationType.STUDENT_DAILY_QUIZ_REMINDER,
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ',
        currentStreak: currentStreak.toString(),
        reminderType: 'daily',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Get reminder job status
  public getReminderJobStatus(): {
    streakReminder: { isRunning: boolean; schedule: string };
    dailyQuizReminder: { isRunning: boolean; schedule: string };
  } {
    return {
      streakReminder: {
        isRunning: this.streakReminderJob !== null,
        schedule: 'Every hour'
      },
      dailyQuizReminder: {
        isRunning: this.dailyQuizReminderJob !== null,
        schedule: '6:00 PM IST daily'
      }
    };
  }

  // Manually trigger streak reminders (for testing)
  public async triggerStreakRemindersManually(): Promise<any> {
    console.log('🔧 Manually triggering streak reminders...');
    try {
      await this.processStreakReminders();
      return { success: true, message: 'Streak reminders processed successfully' };
    } catch (error) {
      console.error('Error in manual streak reminder trigger:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Manually trigger daily quiz reminders (for testing)
  public async triggerDailyQuizRemindersManually(): Promise<any> {
    console.log('🔧 Manually triggering daily quiz reminders...');
    try {
      await this.sendDailyQuizReminders();
      return { success: true, message: 'Daily quiz reminders sent successfully' };
    } catch (error) {
      console.error('Error in manual daily quiz reminder trigger:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

// Export singleton instance
export const reminderService = ReminderService.getInstance();
