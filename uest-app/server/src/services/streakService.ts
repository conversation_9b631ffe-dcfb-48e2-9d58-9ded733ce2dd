import axios from 'axios';
import { sendPushNotificationToUser, sendStreakReminderNotification, DailyQuizCompletionData } from './pushNotificationService';
import { createNotification } from '@/utils/notifications';
import { UserType } from '@prisma/client';
import cron from 'node-cron';

export interface DailyQuizCompletionRequest {
  studentId: string;
  score: number;
  totalQuestions: number;
  coinEarnings: number;
  timeTaken?: number;
  completedFrom?: 'mobile' | 'web';
}

export interface StreakData {
  studentId: string;
  currentStreak: number;
  longestStreak: number;
  lastQuizDate: Date | null;
  isActive: boolean;
}

export class StreakService {
  private static instance: StreakService;
  private reminderJob: cron.ScheduledTask | null = null;

  private constructor() {}

  public static getInstance(): StreakService {
    if (!StreakService.instance) {
      StreakService.instance = new StreakService();
    }
    return StreakService.instance;
  }

  // Record daily quiz completion and update streak (via uest-uwhiz API)
  async recordDailyQuizCompletion(data: DailyQuizCompletionRequest): Promise<StreakData> {
    const { studentId, score, totalQuestions, coinEarnings, timeTaken, completedFrom = 'mobile' } = data;

    try {
      // Call uest-uwhiz API to get current streak data
      const streakResponse = await axios.get(
        `${process.env.UWHIZ_BACKEND_URL}/mock-exam-streak/student/${studentId}`
      );

      const currentStreak = streakResponse.data?.success ? streakResponse.data.data.currentStreak : 0;
      const longestStreak = streakResponse.data?.success ? streakResponse.data.data.longestStreak : 0;

      // Send completion notification with enhanced data
      const completionData: DailyQuizCompletionData = {
        studentId,
        score,
        totalQuestions,
        coinEarnings,
        streakCount: currentStreak,
        timeTaken,
        completedFrom
      };

      await this.sendQuizCompletionNotification(completionData);

      // Send milestone notification for special streaks
      if (currentStreak > 1 && this.isStreakMilestone(currentStreak)) {
        await this.sendStreakMilestoneNotification(studentId, currentStreak);
      }

      console.log(`✅ Daily quiz completion notification sent for student: ${studentId} (streak: ${currentStreak})`);

      return {
        studentId,
        currentStreak,
        longestStreak,
        lastQuizDate: new Date(),
        isActive: true
      };

    } catch (error) {
      console.error('Error recording daily quiz completion:', error);

      // Fallback: Send notification without streak data
      const completionData: DailyQuizCompletionData = {
        studentId,
        score,
        totalQuestions,
        coinEarnings,
        streakCount: 0,
        timeTaken,
        completedFrom
      };

      await this.sendQuizCompletionNotification(completionData);

      throw error;
    }
  }

  // Get streak information for a student (via uest-uwhiz API)
  async getStudentStreak(studentId: string): Promise<StreakData | null> {
    try {
      const response = await axios.get(
        `${process.env.UWHIZ_BACKEND_URL}/mock-exam-streak/student/${studentId}`
      );

      if (!response.data?.success) {
        return null;
      }

      const streakData = response.data.data;
      return {
        studentId,
        currentStreak: streakData.currentStreak || 0,
        longestStreak: streakData.longestStreak || 0,
        lastQuizDate: streakData.lastQuizDate ? new Date(streakData.lastQuizDate) : null,
        isActive: true
      };
    } catch (error) {
      console.error('Error getting student streak:', error);
      return null;
    }
  }

  startStreakReminderSystem(): void {
    this.stopStreakReminderSystem();

    this.reminderJob = cron.schedule('0 * * * *', async () => {
      await this.checkAndSendStreakReminders();
    }, {
      scheduled: true,
      timezone: 'Asia/Kolkata'
    });

    console.log('Streak reminder system started');
  }

  stopStreakReminderSystem(): void {
    if (this.reminderJob) {
      this.reminderJob.stop();
      this.reminderJob = null;
    }
  }

  async checkAndSendStreakReminders(): Promise<void> {
    try {
      const now = new Date();

      if (now.getHours() !== 20 || now.getMinutes() !== 0) {
        return;
      }

      const studentsNeedingReminder = await this.getStudentsWhoNeedReminders();

      if (studentsNeedingReminder.length > 0) {
        console.log(`Sending streak reminders to ${studentsNeedingReminder.length} students`);

        for (const student of studentsNeedingReminder) {
          await this.sendStreakReminderToStudent(student.studentId, student.currentStreak);
        }
      }

    } catch (error) {
      console.error('Streak reminder error:', error);
    }
  }

  private async getStudentsWhoNeedReminders(): Promise<Array<{studentId: string, currentStreak: number}>> {
    try {
      const response = await axios.get(
        `${process.env.UWHIZ_BACKEND_URL}/mock-exam-streak/students-needing-reminders`
      );

      return response.data?.success ? response.data.data || [] : [];
    } catch (error) {
      console.error('Error getting students who need reminders:', error);
      return [];
    }
  }

  private async sendStreakReminderToStudent(studentId: string, currentStreak: number): Promise<void> {
    try {
      await sendStreakReminderNotification({
        studentId,
        currentStreak,
        hoursRemaining: 4
      });

    } catch (error) {
      console.error(`Error sending streak reminder to student ${studentId}:`, error);
    }
  }

  async scheduleStreakReminder(studentId: string): Promise<void> {
    // Handled automatically by cron job
  }

  private async cancelTodayReminder(studentId: string): Promise<void> {
    // Handled automatically by uest-uwhiz
  }

  private async sendQuizCompletionNotification(data: DailyQuizCompletionData): Promise<void> {
    try {
      const { sendDailyQuizCompletionNotification } = await import('./pushNotificationService');
      await sendDailyQuizCompletionNotification(data);
    } catch (error) {
      console.error('Error sending quiz completion notification:', error);
    }
  }

  private isStreakMilestone(streak: number): boolean {
    const milestones = [3, 5, 7, 10, 15, 20, 25, 30, 50, 75, 100];
    return milestones.includes(streak);
  }

  private async sendStreakMilestoneNotification(studentId: string, streakCount: number): Promise<void> {
    const title = `🎯 ${streakCount}-Day Streak Milestone!`;
    const message = `Incredible! You've maintained a ${streakCount}-day daily quiz streak! Keep up the amazing consistency! 🌟`;

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: 'STUDENT_STREAK_MILESTONE' as any,
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ',
        streakCount: streakCount.toString(),
        milestone: true,
        timestamp: new Date().toISOString()
      }
    });
  }

  private async sendStreakBrokenNotification(studentId: string, previousStreak: number): Promise<void> {
    const title = '💔 Streak Broken';
    const message = `Your ${previousStreak}-day streak has ended. Don't worry, start a new one today! Every expert was once a beginner. 💪`;

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: 'STUDENT_STREAK_BROKEN' as any,
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ',
        previousStreak: previousStreak.toString(),
        encouragement: true,
        timestamp: new Date().toISOString()
      }
    });
  }
}

export const streakService = StreakService.getInstance();
